# 婴幼儿社会情绪发展指导数据集 (Infant Social-Emotional Development Guidance Dataset)

## 数据集概述 (Dataset Overview)

本数据集专注于0-3岁婴幼儿社会情绪发展指导，包含多种类型的训练数据，旨在支持智能助手在婴幼儿照护、情绪指导和发展评估方面的能力。数据集基于PEC（Parental Emotion Coaching）理论框架和ASQ:SE-2评估标准构建。

This dataset focuses on social-emotional development guidance for infants aged 0-3 years, containing various types of training data to support AI assistants in infant care, emotional guidance, and developmental assessment. The dataset is built based on PEC (Parental Emotion Coaching) theory framework and ASQ:SE-2 assessment standards.

## 数据集统计 (Dataset Statistics)

- **总数据集数量**: 9个
- **总样本数**: 4,041个
- **总大小**: 6.29MB
- **年龄覆盖**: 0-36个月 (分为6个阶段)
- **发展领域**: 认知、运动、语言、社交、情绪

## 数据集详情 (Dataset Details)

### 1. 结构化场景数据集 (Structured Scenario Datasets)
**6个数据集，共2,641个样本**

#### enhanced_infant_emotion_dataset.json
- **样本数**: 600
- **大小**: 1.2MB
- **描述**: 增强的婴幼儿情绪发展场景数据
- **年龄分布**: 每个年龄段100个样本 (0-1m, 1-6m, 6-12m, 12-18m, 18-24m, 24-36m)
- **发展领域**: 认知(151), 运动(116), 语言(94), 社交(123), 情绪(116)

#### infant_emotion_guidance_dataset.json
- **样本数**: 600
- **大小**: 0.67MB
- **描述**: 婴幼儿情绪指导场景数据
- **结构**: 与enhanced_infant_emotion_dataset相同

#### parent_education_dataset.json
- **样本数**: 600
- **大小**: 1.2MB
- **描述**: 家长教育指导数据
- **用途**: 训练AI助手为家长提供专业指导

#### professional_training_dataset.json
- **样本数**: 600
- **大小**: 1.2MB
- **描述**: 专业人员培训数据
- **用途**: 支持专业照护人员的培训和指导

#### emotion_coaching_dataset.json
- **样本数**: 116
- **大小**: 0.23MB
- **描述**: 情绪指导专项数据集
- **特点**: 专注于情绪发展领域

#### developmental_assessment_dataset.json
- **样本数**: 125
- **大小**: 0.25MB
- **描述**: 发展评估数据集
- **用途**: 支持婴幼儿发展里程碑评估

### 2. Qwen增强数据集 (Qwen Enhanced Dataset)
**1个数据集，200个样本**

#### qwen_enhanced_infant_emotion_dataset.json
- **样本数**: 200
- **大小**: 0.97MB
- **描述**: 使用Qwen API增强的高质量情绪分析数据
- **特点**: 包含详细的情绪轨迹分析和专业指导策略
- **年龄分布**: 每个年龄段40个样本 (0-6m, 6-12m, 12-18m, 18-24m, 24-36m)

### 3. 对话数据集 (Conversation Dataset)
**1个数据集，600个样本**

#### infant_guidance_conversation_data.json
- **样本数**: 600
- **大小**: 0.34MB
- **描述**: 婴幼儿指导对话数据
- **格式**: 结构化对话格式，包含元数据

### 4. 指令跟随数据集 (Instruction Following Dataset)
**1个数据集，600个样本**

#### infant_guidance_instruction_data.json
- **样本数**: 600
- **大小**: 0.23MB
- **描述**: 指令-输入-输出格式的训练数据
- **用途**: 训练模型理解和执行婴幼儿照护指令

## 数据格式 (Data Format)

### 结构化场景数据格式
```json
{
  "scenario_id": "unique_identifier",
  "age_range": "0-1m|1-6m|6-12m|12-18m|18-24m|24-36m",
  "development_domain": "cognitive|motor|language|social|emotion",
  "scenario_type": "daily_care|play|feeding|sleep|social_interaction",
  "context": {
    "setting": "home|daycare|clinic",
    "participants": ["caregiver", "infant"],
    "background": "scenario_description"
  },
  "infant_behavior": {
    "description": "observed_behavior",
    "emotional_state": "current_emotion",
    "developmental_indicators": ["indicator1", "indicator2"]
  },
  "guidance": {
    "immediate_response": "what_to_do_now",
    "strategies": ["strategy1", "strategy2"],
    "theoretical_basis": "underlying_theory"
  }
}
```

### Qwen增强数据格式
```json
{
  "scenario_id": "qwen_enhanced_age_id",
  "original_topic": "source_topic",
  "original_dialogue": "original_conversation",
  "target_age": "age_range",
  "emotion_analysis": {
    "情感识别与变化": "detailed_emotion_analysis",
    "触发因素分析": "trigger_analysis",
    "指导策略建议": "guidance_strategies"
  },
  "infant_scenario": {
    "场景背景": "adapted_scenario",
    "婴幼儿行为表现": "infant_behaviors",
    "照护者指导方法": "caregiver_guidance"
  }
}
```

## 使用方法 (Usage)

### 加载数据集
```python
import json

# 加载特定数据集
with open('enhanced_infant_emotion_dataset.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"数据集包含 {len(data)} 个样本")
```

### 按年龄段筛选
```python
# 筛选特定年龄段的数据
age_filtered = [item for item in data if item['age_range'] == '12-18m']
```

### 按发展领域筛选
```python
# 筛选情绪发展相关数据
emotion_data = [item for item in data if item['development_domain'] == 'emotion']
```

## 理论基础 (Theoretical Foundation)

### PEC理论框架 (Parental Emotion Coaching)
- **情绪意识**: 识别和理解婴幼儿的情绪状态
- **情绪接纳**: 接受和验证婴幼儿的情绪体验
- **情绪指导**: 提供适当的情绪调节策略和支持

### ASQ:SE-2评估标准
- **社会交往**: 与他人互动的能力
- **情绪调节**: 管理和表达情绪的能力
- **适应行为**: 适应环境变化的能力
- **自主性**: 独立性和自我控制的发展

### 发展里程碑
- **0-1个月**: 基本生理调节和依恋形成
- **1-6个月**: 社交微笑和情绪表达
- **6-12个月**: 陌生人焦虑和社交参照
- **12-18个月**: 自主性发展和情绪调节
- **18-24个月**: 情绪命名和自我意识
- **24-36个月**: 复杂情绪和社交技能

## 应用场景 (Applications)

1. **智能育儿助手**: 为家长提供个性化的育儿指导
2. **专业培训系统**: 支持儿童发展专家和照护人员的培训
3. **发展评估工具**: 辅助婴幼儿发展里程碑的评估
4. **情绪指导系统**: 提供基于科学理论的情绪指导策略
5. **研究支持**: 支持婴幼儿发展相关的学术研究

## 数据质量保证 (Quality Assurance)

- **专业审核**: 基于儿童发展心理学理论构建
- **多样性**: 覆盖不同年龄段和发展领域
- **一致性**: 统一的数据格式和标注标准
- **可扩展性**: 支持后续数据的增量更新

