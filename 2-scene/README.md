---
language: zh
license: apache-2.0
dataset_id: cribhd-infant-monitoring-text
tags:
- infant-monitoring
- scene-description
- child-development
- safety-assessment
- multimodal
- vision-language
- chinese
- healthcare
- developmental-milestones
task_categories:
- text-generation
- text-classification
- question-answering
size_categories:
- 1K<n<10K
---

# CribHD婴幼儿家庭场景运动监测文本数据集

## 数据集概述

**CribHD-Infant-Monitoring-Text** 是一个专门用于婴幼儿家庭场景运动监测的中文文本数据集。该数据集基于CribHD图像数据集，通过多模态AI模型（Qwen-VL）生成了详细的场景描述、发育评估和安全风险分析，为婴幼儿监护、发育评估和安全预警等应用提供高质量的训练数据。

### 🎯 主要特点

- **专业性强**: 基于国家卫健委发育里程碑和WHO标准进行评估
- **结构化描述**: 包含5个维度的专业分析（场景、发育、安全、指导、总结）
- **多场景覆盖**: 涵盖毯子使用、玩具互动和危机场景三大类别
- **高质量标注**: 使用Qwen多模态模型生成，经过质量控制
- **即用格式**: 提供训练就绪的数据分割和多种导出格式

### 📊 数据集统计

| 指标 | 数值 |
|------|------|
| **总样本数** | 1,499 |
| **训练集** | 1,369 (91.3%) |
| **验证集** | 90 (6.0%) |
| **测试集** | 40 (2.7%) |
| **语言** | 中文 |
| **生成模型** | Qwen-VL-Plus |
| **数据来源** | CribHD数据集 |

### 🏷️ 子集分布

| 子集类型 | 样本数 | 描述 |
|----------|--------|------|
| **毯子场景 (Blanket)** | 499 | 婴儿与毯子互动的安全监测场景 |
| **玩具场景 (Toy)** | 1,000 | 婴儿与玩具互动的发育评估场景 |
| **危机场景 (Crisis)** | 120 | 模拟危险情况的安全预警场景 |

## 数据集结构

```
cribhd_text_dataset/
├── README.md                    # 本文档
├── conversion_statistics.json   # 数据集统计信息
├── cribhd_text_dataset.json    # 完整数据集（JSON格式）
├── cribhd_text_dataset.csv     # 表格格式数据
└── training_format/            # 机器学习训练格式
    ├── train.json              # 训练集 (1,369样本)
    ├── valid.json              # 验证集 (90样本)
    └── test.json               # 测试集 (40样本)
```

## 数据格式

### 完整数据格式 (cribhd_text_dataset.json)

```json
{
  "metadata": {
    "source_dataset": "CribHD",
    "conversion_time": "2025-07-20T16:14:31.613029",
    "model_used": "qwen",
    "total_images": 1619,
    "subsets": ["cribhd_b", "cribhd_t", "cribhd_c"]
  },
  "data": [
    {
      "timestamp": "2025-07-20T16:14:40.278459",
      "raw_description": [
        {
          "text": "## 1. 基础场景描述\n- 婴儿年龄估计：6-12个月\n- 婴儿当前姿势和动作状态：婴儿正在睡觉，侧卧在婴儿床上\n- 婴儿床内物品清单：婴儿床、蓝色毯子\n\n## 2. 运动发育评估\n- 大运动技能观察：婴儿能够侧卧并保持稳定，说明其翻身能力较强\n- 精细动作技能观察：婴儿能够紧握被子的一角，说明其抓握能力较好\n- 发育水平评估：根据观察，该婴儿的发育水平正常\n\n## 3. 安全风险评估\n- 玩具安全性分析：无玩具存在\n- 毯子使用安全性：毯子没有覆盖面部或限制活动的风险\n- 风险等级评定：低风险\n\n## 4. 育儿指导建议\n- 针对当前发育阶段的活动建议：继续鼓励婴儿进行翻身练习\n- 安全改进措施：确保婴儿床的安全性\n\n## 5. 专业总结\n- 整体发育状况评价：该婴儿的发育状况正常\n- 重点关注事项：继续关注婴儿的翻身能力"
        }
      ],
      "estimated_age_months": 6,
      "motor_skills": [],
      "safety_assessment": {
        "risks_detected": [],
        "safe_items": [],
        "recommendations": []
      },
      "development_level": "正常",
      "scene_type": "crib",
      "annotations": {
        "image_info": {
          "id": 414,
          "file_name": "train10.jpg",
          "height": 640,
          "width": 640
        }
      }
    }
  ]
}
```

### 训练格式 (training_format/*.json)

```json
[
  {
    "text": [
      {
        "text": "## 1. 基础场景描述\n- 婴儿年龄估计：6-12个月\n..."
      }
    ],
    "labels": {
      "age_months": 6,
      "motor_skills": [],
      "safety_risks": [],
      "hazard_category": "blanket"
    }
  }
]
```

## 使用方法

### 🚀 快速开始

#### 使用Hugging Face Datasets

```python
from datasets import load_dataset

# 加载完整数据集
dataset = load_dataset('json', data_files={
    'train': 'training_format/train.json',
    'validation': 'training_format/valid.json',
    'test': 'training_format/test.json'
})

# 查看数据集信息
print(dataset)
print(dataset['train'][0])
```

#### 直接加载JSON文件

```python
import json
import pandas as pd

# 加载完整数据集
with open('cribhd_text_dataset.json', 'r', encoding='utf-8') as f:
    full_dataset = json.load(f)

# 加载训练数据
with open('training_format/train.json', 'r', encoding='utf-8') as f:
    train_data = json.load(f)

# 加载CSV格式
df = pd.read_csv('cribhd_text_dataset.csv')
```

### 📋 数据字段说明

#### 完整数据格式字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `timestamp` | string | 数据生成时间戳 |
| `raw_description` | list | 原始文本描述（结构化格式） |
| `estimated_age_months` | int | 估计婴儿年龄（月） |
| `motor_skills` | list | 观察到的运动技能 |
| `safety_assessment` | dict | 安全评估结果 |
| `development_level` | string | 发育水平评估 |
| `scene_type` | string | 场景类型 |
| `annotations` | dict | 原始图像标注信息 |

#### 训练格式字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `text` | list | 文本描述内容 |
| `labels.age_months` | int | 婴儿年龄标签 |
| `labels.motor_skills` | list | 运动技能标签 |
| `labels.safety_risks` | list | 安全风险标签 |
| `labels.hazard_category` | string | 危险类别（blanket/toy/crisis_scene） |

### 🎯 应用场景

#### 1. 文本生成任务
```python
# 基于场景描述生成发育评估报告
from transformers import pipeline

generator = pipeline('text-generation', model='your-model')
prompt = "基于以下婴儿场景，生成发育评估：婴儿正在..."
result = generator(prompt, max_length=200)
```

#### 2. 分类任务
```python
# 安全风险分类
from transformers import AutoTokenizer, AutoModelForSequenceClassification

model = AutoModelForSequenceClassification.from_pretrained('your-model')
tokenizer = AutoTokenizer.from_pretrained('your-model')

text = "婴儿床内有小玩具..."
inputs = tokenizer(text, return_tensors="pt")
outputs = model(**inputs)
```

#### 3. 问答任务
```python
# 基于场景描述回答发育相关问题
from transformers import pipeline

qa_pipeline = pipeline('question-answering', model='your-model')
context = "婴儿能够独立坐立，正在用双手抓握玩具..."
question = "这个婴儿的大运动技能发育如何？"
answer = qa_pipeline(question=question, context=context)
```

## 数据质量

### 🔍 质量控制措施

1. **模型一致性**: 全部使用Qwen-VL-Plus模型生成，确保描述风格一致
2. **结构化模板**: 采用5段式专业评估模板，保证内容完整性
3. **专业标准**: 基于国家卫健委发育里程碑和WHO标准
4. **多维度评估**: 涵盖场景、发育、安全、指导、总结五个维度

### 📈 数据统计分析

```python
# 查看数据集统计信息
with open('conversion_statistics.json', 'r', encoding='utf-8') as f:
    stats = json.load(f)

print("数据集统计:")
print(f"总图像数: {stats['总体统计']['总图像数']}")
print(f"子集分布: {stats['子集分布']}")
print(f"年龄分布: {stats['年龄分布']}")
```

### ⚠️ 已知限制

1. **年龄分布单一**: 当前数据主要集中在6-9个月年龄段
2. **语言限制**: 仅支持中文描述
3. **模拟数据**: CRIBHD-C子集为模拟危机场景，非真实情况
4. **标注依赖**: 依赖AI模型生成，可能存在一定偏差

## 引用

如果您在研究中使用了此数据集，请引用：

```bibtex
@dataset{cribhd_infant_monitoring_text_2025,
  title={CribHD婴幼儿家庭场景运动监测文本数据集},
  author={Your Name},
  year={2025},
  publisher={Your Institution},
  version={1.0},
  description={基于CribHD图像数据集生成的婴幼儿监护文本数据集},
  url={https://your-dataset-url}
}
```

## 许可证

本数据集采用 **Apache 2.0** 许可证发布。

### 使用条款
- ✅ 允许商业使用
- ✅ 允许修改和分发
- ✅ 允许私人使用
- ⚠️ 需要保留版权声明
- ⚠️ 需要声明修改内容

### 免责声明
本数据集仅供研究和教育用途。生成的文本描述和评估建议不能替代专业医疗诊断，使用者应当谨慎使用并承担相应责任。

## 贡献与反馈

### 🤝 如何贡献
- 报告数据质量问题
- 提供改进建议
- 分享使用案例
- 贡献新的评估维度

### 📧 联系方式
- 邮箱: <EMAIL>
- GitHub Issues: [项目地址]
- 技术支持: [支持渠道]

## 更新日志

### v1.0 (2025-07-20)
- 🎉 初始版本发布
- 📊 包含1,499个高质量样本
- 🔧 支持多种数据格式
- 📚 完整的文档和使用示例

---

**致谢**: 感谢CribHD原始数据集的提供者、Qwen团队提供的多模态模型支持，以及所有为婴幼儿安全监护技术发展做出贡献的研究者们。